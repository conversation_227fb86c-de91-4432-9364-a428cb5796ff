import { SecuritySettingsCards } from "@daveyplate/better-auth-ui";
import { Settings, Shield, Trash2 } from "lucide-react";
import { headers } from "next/headers";
import Link from "next/link";
import { redirect } from "next/navigation";
import { AppSidebar } from "@/components/app-sidebar";
import { SiteHeader } from "@/components/site-header";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { auth } from "@/lib/auth";
import { cn } from "@/lib/utils";

export default async function SecuritySettingsPage() {
  // Protect the security settings route - redirect to sign-in if not authenticated
  const sessionData = await auth.api.getSession({
    headers: await headers(),
  });

  if (!sessionData) {
    redirect("/auth/sign-in?redirectTo=/dashboard/settings/security");
  }

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-6 py-4 md:py-6 lg:flex-row">
              {/* Left Navigation */}
              <div className="px-4 lg:w-64 lg:flex-shrink-0 lg:px-6">
                <nav className="space-y-1">
                  <h2 className="mb-4 font-semibold text-lg">Settings</h2>
                  <div className="flex gap-2 overflow-x-auto lg:flex-col lg:space-y-1 lg:overflow-visible">
                    <Link
                      href="/dashboard/settings"
                      className={cn(
                        "flex items-center gap-3 whitespace-nowrap rounded-lg px-3 py-2 font-medium text-sm transition-colors lg:w-full",
                        "text-muted-foreground hover:bg-muted hover:text-foreground"
                      )}
                    >
                      <Settings className="h-4 w-4" />
                      General Settings
                    </Link>
                    <Link
                      href="/dashboard/settings/security"
                      className={cn(
                        "flex items-center gap-3 whitespace-nowrap rounded-lg px-3 py-2 font-medium text-sm transition-colors lg:w-full",
                        "bg-muted text-foreground"
                      )}
                    >
                      <Shield className="h-4 w-4" />
                      Security
                    </Link>
                  </div>
                </nav>
              </div>

              {/* Main Content */}
              <div className="flex-1 px-4 lg:px-6">
                <div className="max-w-2xl">
                  <SecuritySettingsCards />
                </div>
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
