"use client";

import { AccountSettingsCards } from "@daveyplate/better-auth-ui";
import { Settings, Shield } from "lucide-react";
import { useState } from "react";
import { AppSidebar } from "@/components/app-sidebar";
import { SiteHeader } from "@/components/site-header";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";

// Placeholder component for SecuritySettingsCards
function SecuritySettingsCards() {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="font-medium text-lg">Security Settings</h3>
        <p className="text-muted-foreground text-sm">
          Manage your account security and authentication preferences.
        </p>
      </div>
      <div className="rounded-lg border p-6">
        <p className="text-muted-foreground text-sm">
          Security settings will be implemented here. This could include:
        </p>
        <ul className="mt-2 list-inside list-disc space-y-1 text-muted-foreground text-sm">
          <li>Two-factor authentication</li>
          <li>Password management</li>
          <li>Login sessions</li>
          <li>Security logs</li>
        </ul>
      </div>
    </div>
  );
}

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState<"general" | "security">("general");

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-1 gap-6 py-4 md:py-6">
              {/* Left Navigation */}
              <div className="w-64 flex-shrink-0 px-4 lg:px-6">
                <nav className="space-y-1">
                  <h2 className="mb-4 font-semibold text-lg">Settings</h2>
                  <div className="space-y-1">
                    <button
                      type="button"
                      onClick={() => setActiveTab("general")}
                      className={cn(
                        "flex w-full items-center gap-3 rounded-lg px-3 py-2 font-medium text-sm transition-colors",
                        activeTab === "general"
                          ? "bg-muted text-foreground"
                          : "text-muted-foreground hover:bg-muted hover:text-foreground"
                      )}
                    >
                      <Settings className="h-4 w-4" />
                      General Settings
                    </button>
                    <button
                      type="button"
                      onClick={() => setActiveTab("security")}
                      className={cn(
                        "flex w-full items-center gap-3 rounded-lg px-3 py-2 font-medium text-sm transition-colors",
                        activeTab === "security"
                          ? "bg-muted text-foreground"
                          : "text-muted-foreground hover:bg-muted hover:text-foreground"
                      )}
                    >
                      <Shield className="h-4 w-4" />
                      Security
                    </button>
                  </div>
                </nav>
              </div>

              {/* Main Content */}
              <div className="flex-1 px-4 lg:px-6">
                <div className="max-w-2xl">
                  {activeTab === "general" ? (
                    <AccountSettingsCards />
                  ) : (
                    <SecuritySettingsCards />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
